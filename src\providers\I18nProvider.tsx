import React, { ReactNode, useEffect } from 'react';
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { getLocales } from 'react-native-localize';

// Import translation files
import en from '@/locales/en.json';
import fr from '@/locales/fr.json';
import ar from '@/locales/ar.json';

interface I18nProviderProps {
  children: ReactNode;
}

// Initialize i18n
i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: { translation: en },
      fr: { translation: fr },
      ar: { translation: ar },
    },
    lng: 'en', // default language
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
    react: {
      useSuspense: false,
    },
  });

export const I18nProvider: React.FC<I18nProviderProps> = ({ children }) => {
  useEffect(() => {
    // Get device locale and set it as the app language
    const locales = getLocales();
    if (locales && locales.length > 0) {
      const deviceLanguage = locales[0].languageCode;
      
      // Check if we support the device language
      const supportedLanguages = ['en', 'fr', 'ar'];
      if (supportedLanguages.includes(deviceLanguage)) {
        i18n.changeLanguage(deviceLanguage);
      }
    }
  }, []);

  return <>{children}</>;
};

import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { DashboardNavigator } from './DashboardNavigator';
import { AppointmentsNavigator } from './AppointmentsNavigator';
import { BusinessNavigator } from './BusinessNavigator';
import { CustomersNavigator } from './CustomersNavigator';
import { MessagesNavigator } from './MessagesNavigator';
import { theme } from '@/constants/theme';
import type { MainTabParamList } from '@/types/navigation';

const Tab = createBottomTabNavigator<MainTabParamList>();

export const MainNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      initialRouteName="Dashboard"
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'dashboard';
              break;
            case 'Appointments':
              iconName = 'event';
              break;
            case 'Business':
              iconName = 'business';
              break;
            case 'Customers':
              iconName = 'people';
              break;
            case 'Messages':
              iconName = 'chat';
              break;
            default:
              iconName = 'help';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.textSecondary,
        tabBarStyle: {
          backgroundColor: theme.colors.background,
          borderTopColor: theme.colors.border,
          borderTopWidth: 1,
          paddingBottom: 8,
          paddingTop: 8,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: theme.typography.fontSize.sm,
          fontFamily: theme.typography.fontFamily.primary,
          fontWeight: theme.typography.fontWeight.medium,
        },
      })}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardNavigator}
        options={{
          tabBarLabel: 'Dashboard',
        }}
      />
      <Tab.Screen
        name="Appointments"
        component={AppointmentsNavigator}
        options={{
          tabBarLabel: 'Appointments',
        }}
      />
      <Tab.Screen
        name="Business"
        component={BusinessNavigator}
        options={{
          tabBarLabel: 'Business',
        }}
      />
      <Tab.Screen
        name="Customers"
        component={CustomersNavigator}
        options={{
          tabBarLabel: 'Customers',
        }}
      />
      <Tab.Screen
        name="Messages"
        component={MessagesNavigator}
        options={{
          tabBarLabel: 'Messages',
        }}
      />
    </Tab.Navigator>
  );
};

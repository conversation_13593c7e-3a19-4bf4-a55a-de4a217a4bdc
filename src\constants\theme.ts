export const colors = {
  // Primary Colors - Dalti Brand
  primary: '#15424E',           // Stormy Sea - Main brand color
  primaryLight: '#E8F4F8',      // Light variant for backgrounds
  primaryVariant: '#0D3339',    // Darker variant for depth
  secondary: '#4ECDC4',         // <PERSON><PERSON> - Secondary accent
  accent: '#FFE66D',            // Golden Yellow - Highlight color
  
  // Neutral Colors
  background: '#FFFFFF',        // Pure white background
  surface: '#FAFAFA',          // Light gray surface
  surfaceVariant: '#F5F5F5',   // Slightly darker surface
  
  // Text Colors
  textPrimary: '#1A1A1A',      // Almost black for primary text
  textSecondary: '#666666',     // Gray for secondary text
  textTertiary: '#999999',      // Light gray for tertiary text
  textOnPrimary: '#FFFFFF',     // White text on primary background
  textOnSecondary: '#FFFFFF',   // White text on secondary background
  
  // Status Colors
  success: '#4CAF50',           // Green for success states
  warning: '#FF9800',           // Orange for warning states
  error: '#F44336',             // Red for error states
  info: '#2196F3',              // Blue for info states
  
  // Border Colors
  border: '#E0E0E0',            // Light gray for borders
  borderFocus: '#15424E',       // Primary color for focused borders
  
  // Shadow Colors
  shadow: '#000000',            // Black for shadows
  shadowLight: 'rgba(0, 0, 0, 0.1)',
  shadowMedium: 'rgba(0, 0, 0, 0.2)',
  shadowDark: 'rgba(0, 0, 0, 0.3)',
};

export const typography = {
  fontFamily: {
    primary: 'Changa',           // Arabic-friendly font
    secondary: 'Helvetica Neue',
    fallback: 'Arial, sans-serif',
  },
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  fontWeight: {
    light: '300',
    regular: '400',
    medium: '500',
    semiBold: '600',
    bold: '700',
  },
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
  },
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
};

export const borderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  round: 50,
};

export const elevation = {
  none: 0,
  sm: 2,
  md: 4,
  lg: 8,
  xl: 16,
};

export const theme = {
  colors,
  typography,
  spacing,
  borderRadius,
  elevation,
};

# Dalti Provider - React Native

A comprehensive business management mobile application for service providers, built with React Native. This app enables healthcare professionals, beauty specialists, consultants, and other service providers to efficiently manage their business operations, appointments, and customer relationships.

## 🚀 Features

### 🔐 Authentication & Security
- Secure login with email/phone and password
- OTP-based registration and password recovery
- JWT token management with automatic refresh
- Biometric authentication support
- Secure storage using Keychain (iOS) and Keystore (Android)

### 🏢 Business Management
- **Multi-location Management**: Create and manage multiple business locations
- **Service Catalog**: Define services with pricing, duration, and categories
- **Queue Management**: Real-time queue monitoring and management
- **Provider Profile**: Comprehensive business profile management

### 📅 Appointment System
- **Calendar Integration**: Day, week, and month views
- **Appointment Scheduling**: Create, edit, and cancel appointments
- **Time Slot Management**: Availability and conflict detection
- **Status Tracking**: Pending, confirmed, completed, cancelled states

### 👥 Customer Management
- **Customer Profiles**: Detailed customer information and history
- **Relationship Management**: Track customer preferences and notes
- **Search & Filter**: Advanced customer search capabilities

### 💬 Real-time Communication
- **Messaging System**: Direct communication with customers
- **Push Notifications**: Firebase Cloud Messaging integration
- **WebSocket Support**: Real-time updates and notifications

### 📊 Analytics & Reporting
- **Dashboard Overview**: Business performance metrics
- **Revenue Tracking**: Financial analytics and reporting
- **Appointment Analytics**: Booking trends and statistics

### 🌍 Internationalization
- **Multi-language Support**: English, French, Arabic
- **RTL Layout**: Full right-to-left layout support for Arabic
- **Localized Content**: Date, time, and number formatting

## 🛠 Technology Stack

### Core Technologies
- **React Native 0.73**: Cross-platform mobile development
- **TypeScript**: Type-safe development
- **React Navigation 6**: Navigation and routing

### State Management
- **Zustand**: Lightweight state management
- **React Query**: Server state management and caching

### UI & Styling
- **Styled Components**: CSS-in-JS styling
- **Material Design 3**: Design system principles
- **React Native Vector Icons**: Icon library

### Backend Integration
- **Axios**: HTTP client with interceptors
- **RESTful API**: Integration with Dalti backend services

### Security & Storage
- **React Native Keychain**: Secure credential storage
- **Certificate Pinning**: Enhanced API security

### Development Tools
- **ESLint**: Code linting and formatting
- **Prettier**: Code formatting
- **Jest**: Unit testing framework
- **Detox**: End-to-end testing

## 📱 Platform Support

- **iOS**: 12.0+
- **Android**: API level 21+ (Android 5.0)

## 🏗 Architecture

The application follows **Clean Architecture** principles with clear separation of concerns:

```
src/
├── components/          # Reusable UI components
├── screens/            # Screen components
├── navigation/         # Navigation configuration
├── services/           # API services and business logic
├── stores/             # State management (Zustand stores)
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
├── types/              # TypeScript type definitions
└── constants/          # App constants and configuration
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- React Native CLI
- Xcode (for iOS development)
- Android Studio (for Android development)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Zedster07/dalti-provider-react-native.git
   cd dalti-provider-react-native
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **iOS Setup**
   ```bash
   cd ios && pod install && cd ..
   ```

4. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

### Running the App

#### iOS
```bash
npm run ios
```

#### Android
```bash
npm run android
```

#### Development Server
```bash
npm start
```

## 🧪 Testing

### Unit Tests
```bash
npm run test
npm run test:watch
npm run test:coverage
```

### End-to-End Tests
```bash
# Build for testing
npm run test:e2e:build:ios
npm run test:e2e:build:android

# Run E2E tests
npm run test:e2e:ios
npm run test:e2e:android
```

### Code Quality
```bash
npm run lint
npm run type-check
npm run format
```

## 📦 Build & Deployment

### Development Build
```bash
npm run build:android
npm run build:ios
```

### Production Deployment
The app uses GitHub Actions for automated CI/CD:
- **Staging**: Automatic deployment to Firebase App Distribution on `develop` branch
- **Production**: Deployment to App Store and Google Play on `main` branch

## 🔧 Configuration

### Environment Variables
```env
API_BASE_URL=https://dapi-test.adscloud.org:8443
FIREBASE_API_KEY=your_firebase_api_key
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
```

### Firebase Setup
1. Create a Firebase project
2. Add iOS and Android apps
3. Download configuration files:
   - `GoogleService-Info.plist` (iOS)
   - `google-services.json` (Android)

## 📚 Documentation

- [API Documentation](./docs/api-specifications.md)
- [Architecture Guide](./docs/architecture/clean-architecture.md)
- [Component Library](./docs/design-system/component-library.md)
- [Development Standards](./docs/security/coding-standards.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Commit Convention
```
type(scope): description

Examples:
feat(auth): add biometric authentication
fix(api): handle network timeout errors
docs(readme): update installation instructions
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Dalti Team**: Original Flutter app development
- **React Native Community**: Excellent libraries and tools
- **Material Design**: Design system guidelines

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Contact the development team
- Check the documentation

---

**Built with ❤️ by the Dalti Team**

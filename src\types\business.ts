// Business Location Types
export interface Location {
  id: number;
  providerId: number;
  name: string;
  address: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  phone?: string;
  email?: string;
  latitude?: number;
  longitude?: number;
  isActive: boolean;
  workingHours: WorkingHours[];
  createdAt: string;
  updatedAt: string;
}

export interface WorkingHours {
  dayOfWeek: number; // 0 = Sunday, 1 = Monday, etc.
  openTime: string; // HH:mm format
  closeTime: string; // HH:mm format
  isOpen: boolean;
}

export interface CreateLocationRequest {
  name: string;
  address: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  phone?: string;
  email?: string;
  latitude?: number;
  longitude?: number;
  workingHours: WorkingHours[];
}

// Service Types
export interface Service {
  id: number;
  providerId: number;
  locationId?: number;
  categoryId: number;
  name: string;
  description?: string;
  price: number;
  currency: string;
  duration: number; // in minutes
  isActive: boolean;
  maxAdvanceBooking: number; // days
  minAdvanceBooking: number; // hours
  createdAt: string;
  updatedAt: string;
  category: ServiceCategory;
}

export interface ServiceCategory {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
}

export interface CreateServiceRequest {
  locationId?: number;
  categoryId: number;
  name: string;
  description?: string;
  price: number;
  currency: string;
  duration: number;
  maxAdvanceBooking: number;
  minAdvanceBooking: number;
}

// Queue Types
export interface Queue {
  id: number;
  providerId: number;
  locationId: number;
  serviceId: number;
  name: string;
  description?: string;
  maxCapacity: number;
  currentCount: number;
  estimatedWaitTime: number; // in minutes
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  location: Location;
  service: Service;
}

export interface CreateQueueRequest {
  locationId: number;
  serviceId: number;
  name: string;
  description?: string;
  maxCapacity: number;
}

// Provider Category Types
export interface ProviderCategory {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  isActive: boolean;
}

// Dashboard Analytics Types
export interface DashboardOverview {
  totalAppointments: number;
  todayAppointments: number;
  totalCustomers: number;
  totalRevenue: number;
  averageRating: number;
  totalReviews: number;
  activeQueues: number;
  pendingAppointments: number;
}

export interface QuickStats {
  appointmentsToday: number;
  appointmentsTomorrow: number;
  queueLength: number;
  averageWaitTime: number;
  completedToday: number;
  cancelledToday: number;
}

export interface TodaySchedule {
  appointments: Appointment[];
  totalCount: number;
  completedCount: number;
  pendingCount: number;
  cancelledCount: number;
}

// Common Types
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface Appointment {
  id: number;
  providerId: number;
  customerId: number;
  serviceId: number;
  locationId: number;
  date: string;
  startTime: string;
  endTime: string;
  status: AppointmentStatus;
  notes?: string;
  price: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
  customer: Customer;
  service: Service;
  location: Location;
}

export enum AppointmentStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  NO_SHOW = 'no_show',
}

export interface Customer {
  id: number;
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  avatar?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

# Project Initialization Guide

## 🚀 Step-by-Step Project Setup

### 1. Create React Native Project
```bash
# Create new React Native project with TypeScript
npx react-native@latest init DaltiProviderRN --template react-native-template-typescript

# Navigate to project directory
cd DaltiProviderRN

# Initialize git repository
git init
git add .
git commit -m "Initial React Native project setup"
```

### 2. Project Structure Setup
```bash
# Create recommended folder structure
mkdir -p src/{components,features,hooks,navigation,services,stores,types,utils,constants,assets}
mkdir -p src/components/{common,forms,navigation}
mkdir -p src/features/{auth,dashboard,appointments,locations,services,customers,messages,notifications,profile,settings,shared}
mkdir -p src/services/{api,storage,firebase,websocket}
mkdir -p src/stores/{auth,app,business}
mkdir -p docs/{analysis,architecture,requirements,design-system,security,setup}

# Move App.tsx to src/
mv App.tsx src/
```

### 3. Install Core Dependencies
```bash
# Navigation dependencies
npm install @react-navigation/native @react-navigation/stack @react-navigation/bottom-tabs @react-navigation/drawer
npm install react-native-screens react-native-safe-area-context react-native-gesture-handler

# State management
npm install zustand @tanstack/react-query @tanstack/react-query-devtools

# HTTP client and utilities
npm install axios react-native-mmkv react-native-keychain
npm install react-native-device-info react-native-permissions

# UI and styling
npm install react-native-vector-icons react-native-paper styled-components
npm install react-native-super-grid react-native-modal

# Date and utilities
npm install date-fns lodash react-native-uuid

# Forms and validation
npm install react-hook-form @hookform/resolvers zod

# Internationalization
npm install react-native-localize i18next react-i18next
```

### 4. Install Firebase Dependencies
```bash
# Firebase core and services
npm install @react-native-firebase/app
npm install @react-native-firebase/messaging
npm install @react-native-firebase/analytics
npm install @react-native-firebase/storage
npm install @react-native-firebase/crashlytics
```

### 5. Install Development Dependencies
```bash
# Testing
npm install --save-dev @testing-library/react-native @testing-library/jest-native
npm install --save-dev jest-environment-jsdom

# Linting and formatting
npm install --save-dev eslint prettier husky lint-staged
npm install --save-dev @typescript-eslint/eslint-plugin @typescript-eslint/parser
npm install --save-dev eslint-plugin-react eslint-plugin-react-hooks

# Type definitions
npm install --save-dev @types/lodash @types/react-test-renderer
npm install --save-dev @types/styled-components @types/styled-components-react-native

# Development tools
npm install --save-dev reactotron-react-native reactotron-zustand
npm install --save-dev flipper-plugin-react-query
```

## ⚙️ Configuration Files

### 1. TypeScript Configuration
```json
// tsconfig.json
{
  "extends": "@react-native/typescript-config/tsconfig.json",
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "baseUrl": "./src",
    "paths": {
      "@/*": ["*"],
      "@/components/*": ["components/*"],
      "@/features/*": ["features/*"],
      "@/services/*": ["services/*"],
      "@/stores/*": ["stores/*"],
      "@/utils/*": ["utils/*"],
      "@/types/*": ["types/*"],
      "@/constants/*": ["constants/*"],
      "@/hooks/*": ["hooks/*"],
      "@/navigation/*": ["navigation/*"]
    }
  },
  "include": ["src/**/*", "index.js"],
  "exclude": ["node_modules", "android", "ios", "__tests__"]
}
```

### 2. Babel Configuration
```javascript
// babel.config.js
module.exports = {
  presets: ['module:metro-react-native-babel-preset'],
  plugins: [
    [
      'module-resolver',
      {
        root: ['./src'],
        alias: {
          '@': './src',
          '@/components': './src/components',
          '@/features': './src/features',
          '@/services': './src/services',
          '@/stores': './src/stores',
          '@/utils': './src/utils',
          '@/types': './src/types',
          '@/constants': './src/constants',
          '@/hooks': './src/hooks',
          '@/navigation': './src/navigation',
        },
      },
    ],
    'react-native-reanimated/plugin',
  ],
};
```

### 3. Metro Configuration
```javascript
// metro.config.js
const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

const config = {
  resolver: {
    alias: {
      '@': './src',
      '@/components': './src/components',
      '@/features': './src/features',
      '@/services': './src/services',
      '@/stores': './src/stores',
      '@/utils': './src/utils',
      '@/types': './src/types',
      '@/constants': './src/constants',
      '@/hooks': './src/hooks',
      '@/navigation': './src/navigation',
    },
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
```

### 4. ESLint Configuration
```json
// .eslintrc.js
module.exports = {
  root: true,
  extends: [
    '@react-native-community',
    '@typescript-eslint/recommended',
    'prettier',
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'react-hooks'],
  rules: {
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'warn',
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',
    'react-native/no-inline-styles': 'error',
    'prefer-const': 'error',
    'no-var': 'error',
  },
  settings: {
    'import/resolver': {
      'babel-module': {},
    },
  },
};
```

### 5. Prettier Configuration
```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
```

## 🔥 Firebase Setup

### 1. Android Firebase Configuration
```bash
# Download google-services.json from Firebase Console
# Place in android/app/ directory

# Add to android/build.gradle (project level)
echo 'classpath "com.google.gms:google-services:4.3.15"' >> android/build.gradle

# Add to android/app/build.gradle
echo 'apply plugin: "com.google.gms.google-services"' >> android/app/build.gradle
```

### 2. iOS Firebase Configuration
```bash
# Download GoogleService-Info.plist from Firebase Console
# Place in ios/ directory

# Install iOS dependencies
cd ios && pod install && cd ..
```

### 3. Firebase Configuration File
```typescript
// src/services/firebase/config.ts
import { FirebaseOptions } from '@react-native-firebase/app';

export const firebaseConfig: FirebaseOptions = {
  apiKey: 'AIzaSyC946kqiZ2Eoc88vslGXFxVa-6yrWicVec',
  authDomain: 'dalti-prod.firebaseapp.com',
  projectId: 'dalti-prod',
  storageBucket: 'dalti-prod.firebasestorage.app',
  messagingSenderId: '1060372851323',
  appId: '1:1060372851323:web:690318c8147b5c8a0690de',
  measurementId: 'G-Q0BSTXW5FL',
};
```

## 📱 Platform-Specific Setup

### Android Permissions
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.VIBRATE" />
```

### iOS Permissions
```xml
<!-- ios/DaltiProviderRN/Info.plist -->
<key>NSLocationWhenInUseUsageDescription</key>
<string>This app needs location access to show nearby services.</string>
<key>NSCameraUsageDescription</key>
<string>This app needs camera access to scan QR codes.</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>This app needs photo library access to upload images.</string>
```

## 🧪 Testing Setup

### Jest Configuration
```javascript
// jest.config.js
module.exports = {
  preset: 'react-native',
  setupFilesAfterEnv: ['<rootDir>/src/test/setup.ts'],
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}',
  ],
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/test/**',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
};
```

## 📦 Package.json Scripts
```json
{
  "scripts": {
    "start": "react-native start",
    "android": "react-native run-android",
    "ios": "react-native run-ios",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint . --ext .js,.jsx,.ts,.tsx",
    "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix",
    "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,md}\"",
    "type-check": "tsc --noEmit",
    "clean": "react-native clean-project-auto",
    "postinstall": "cd ios && pod install && cd ..",
    "build:android": "cd android && ./gradlew assembleRelease",
    "build:ios": "cd ios && xcodebuild -workspace DaltiProviderRN.xcworkspace -scheme DaltiProviderRN -configuration Release"
  }
}
```

## ✅ Verification Steps

### 1. Test Project Setup
```bash
# Verify TypeScript compilation
npm run type-check

# Run linting
npm run lint

# Run tests
npm test

# Start Metro bundler
npm start
```

### 2. Test Platform Builds
```bash
# Test Android build
npm run android

# Test iOS build (macOS only)
npm run ios
```

### 3. Verify Firebase Integration
```bash
# Check Firebase configuration
npx react-native run-android
# Look for Firebase initialization logs in console
```

This comprehensive project initialization guide ensures a properly configured React Native project ready for the Dalti Provider application development.

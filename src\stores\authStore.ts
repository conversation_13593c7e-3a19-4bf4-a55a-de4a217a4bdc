import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

import { authService } from '@/services/authService';
import { secureStorage } from '@/utils/secureStorage';
import type { AuthStore, LoginCredentials, RegisterData } from '@/types/auth';

export const useAuthStore = create<AuthStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      user: null,
      provider: null,
      sessionId: null,
      isAuthenticated: false,
      isLoading: true,
      error: null,

      // Actions
      login: async (credentials: LoginCredentials) => {
        try {
          set({ isLoading: true, error: null });
          
          const response = await authService.login(credentials);
          
          // Store session securely
          await secureStorage.setItem('sessionId', response.sessionId);
          await secureStorage.setItem('user', JSON.stringify(response.user));
          await secureStorage.setItem('provider', JSON.stringify(response.provider));
          
          set({
            user: response.user,
            provider: response.provider,
            sessionId: response.sessionId,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Login failed';
          set({
            error: errorMessage,
            isLoading: false,
            isAuthenticated: false,
          });
          throw error;
        }
      },

      register: async (data: RegisterData) => {
        try {
          set({ isLoading: true, error: null });
          
          const response = await authService.register(data);
          
          // Store session securely
          await secureStorage.setItem('sessionId', response.sessionId);
          await secureStorage.setItem('user', JSON.stringify(response.user));
          await secureStorage.setItem('provider', JSON.stringify(response.provider));
          
          set({
            user: response.user,
            provider: response.provider,
            sessionId: response.sessionId,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Registration failed';
          set({
            error: errorMessage,
            isLoading: false,
            isAuthenticated: false,
          });
          throw error;
        }
      },

      logout: async () => {
        try {
          const { sessionId } = get();
          
          if (sessionId) {
            await authService.logout();
          }
          
          // Clear stored data
          await secureStorage.removeItem('sessionId');
          await secureStorage.removeItem('user');
          await secureStorage.removeItem('provider');
          
          set({
            user: null,
            provider: null,
            sessionId: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          console.error('Logout error:', error);
          // Force logout even if API call fails
          await secureStorage.removeItem('sessionId');
          await secureStorage.removeItem('user');
          await secureStorage.removeItem('provider');
          
          set({
            user: null,
            provider: null,
            sessionId: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      },

      sendOtp: async (request) => {
        try {
          set({ isLoading: true, error: null });
          await authService.sendOtp(request);
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to send OTP';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },

      verifyOtp: async (request) => {
        try {
          set({ isLoading: true, error: null });
          await authService.verifyOtp(request);
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'OTP verification failed';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },

      forgotPassword: async (request) => {
        try {
          set({ isLoading: true, error: null });
          await authService.forgotPassword(request);
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to send reset email';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },

      resetPassword: async (request) => {
        try {
          set({ isLoading: true, error: null });
          await authService.resetPassword(request);
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Password reset failed';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },

      changePassword: async (request) => {
        try {
          set({ isLoading: true, error: null });
          await authService.changePassword(request);
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Password change failed';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },

      updateProfile: async (data) => {
        try {
          set({ isLoading: true, error: null });
          const updatedUser = await authService.updateProfile(data);
          
          // Update stored user data
          await secureStorage.setItem('user', JSON.stringify(updatedUser));
          
          set({
            user: updatedUser,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Profile update failed';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },

      refreshToken: async () => {
        try {
          const response = await authService.refreshToken();
          
          // Update session
          await secureStorage.setItem('sessionId', response.sessionId);
          
          set({
            sessionId: response.sessionId,
            error: null,
          });
        } catch (error) {
          console.error('Token refresh failed:', error);
          // Force logout on refresh failure
          get().logout();
        }
      },

      clearError: () => set({ error: null }),
      
      setLoading: (loading: boolean) => set({ isLoading: loading }),

      // Initialize auth state from storage
      initializeAuth: async () => {
        try {
          const sessionId = await secureStorage.getItem('sessionId');
          const userStr = await secureStorage.getItem('user');
          const providerStr = await secureStorage.getItem('provider');

          if (sessionId && userStr && providerStr) {
            const user = JSON.parse(userStr);
            const provider = JSON.parse(providerStr);

            set({
              user,
              provider,
              sessionId,
              isAuthenticated: true,
              isLoading: false,
            });
          } else {
            set({ isLoading: false });
          }
        } catch (error) {
          console.error('Auth initialization error:', error);
          set({ isLoading: false });
        }
      },
    }),
    { name: 'auth-store' }
  )
);

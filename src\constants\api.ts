// API Configuration
export const API_CONFIG = {
  BASE_URL: {
    PRODUCTION: 'https://dapi.adscloud.org',
    DEVELOPMENT: 'https://dapi-test.adscloud.org:8443',
  },
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
};

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/api/auth/provider/login',
    REGISTER: '/api/auth/provider/verify-otp-register',
    SEND_OTP: '/api/auth/provider/send-otp',
    LOGOUT: '/api/auth/logout',
    REFRESH_TOKEN: '/api/auth/refresh',
    FORGOT_PASSWORD: '/api/auth/provider/forgot-password',
    RESET_PASSWORD: '/api/auth/provider/reset-password',
  },
  
  // Provider Profile
  PROVIDER: {
    PROFILE: '/api/auth/provider/profile',
    UPDATE_PROFILE: '/api/auth/provider/profile',
    CHANGE_PASSWORD: '/api/auth/provider/change-password',
    UPLOAD_AVATAR: '/api/auth/provider/avatar',
  },
  
  // Business Management
  LOCATIONS: {
    LIST: '/api/auth/providers/locations',
    CREATE: '/api/auth/providers/locations',
    UPDATE: (id: number) => `/api/auth/providers/locations/${id}`,
    DELETE: (id: number) => `/api/auth/providers/locations/${id}`,
    GET: (id: number) => `/api/auth/providers/locations/${id}`,
  },
  
  SERVICES: {
    LIST: '/api/auth/providers/services',
    CREATE: '/api/auth/providers/services',
    UPDATE: (id: number) => `/api/auth/providers/services/${id}`,
    DELETE: (id: number) => `/api/auth/providers/services/${id}`,
    GET: (id: number) => `/api/auth/providers/services/${id}`,
  },
  
  QUEUES: {
    LIST: '/api/auth/providers/queues',
    CREATE: '/api/auth/providers/queues',
    UPDATE: (id: number) => `/api/auth/providers/queues/${id}`,
    DELETE: (id: number) => `/api/auth/providers/queues/${id}`,
    GET: (id: number) => `/api/auth/providers/queues/${id}`,
  },
  
  // Appointments
  APPOINTMENTS: {
    LIST: '/api/auth/providers/appointments',
    CREATE: '/api/auth/providers/appointments',
    UPDATE: (id: number) => `/api/auth/providers/appointments/${id}`,
    CANCEL: (id: number) => `/api/auth/providers/appointments/${id}/cancel`,
    GET: (id: number) => `/api/auth/providers/appointments/${id}`,
    CALENDAR: '/api/auth/providers/appointments/calendar',
  },
  
  // Customers
  CUSTOMERS: {
    LIST: '/api/auth/providers/customers',
    CREATE: '/api/auth/providers/customers',
    UPDATE: (id: number) => `/api/auth/providers/customers/${id}`,
    GET: (id: number) => `/api/auth/providers/customers/${id}`,
    SEARCH: '/api/auth/providers/customers/search',
  },
  
  // Messaging
  CONVERSATIONS: {
    LIST: '/api/auth/mobile/conversations',
    GET: (id: number) => `/api/auth/mobile/conversations/${id}`,
    MESSAGES: (id: number) => `/api/auth/mobile/conversations/${id}/messages`,
    SEND_MESSAGE: (id: number) => `/api/auth/mobile/conversations/${id}/messages`,
  },
  
  // Notifications
  NOTIFICATIONS: {
    LIST: '/api/auth/notifications',
    MARK_READ: (id: number) => `/api/auth/notifications/${id}/read`,
    MARK_ALL_READ: '/api/auth/notifications/read-all',
    SETTINGS: '/api/auth/notifications/settings',
  },
  
  // Dashboard
  DASHBOARD: {
    OVERVIEW: '/api/provider/dashboard/overview',
    TODAY_SCHEDULE: '/api/provider/dashboard/schedule/today',
    QUICK_STATS: '/api/provider/dashboard/quick-stats',
    ANALYTICS: '/api/provider/dashboard/analytics',
  },
  
  // Categories
  CATEGORIES: {
    PROVIDER_CATEGORIES: '/api/provider-categories',
    SERVICE_CATEGORIES: '/api/service-categories',
  },
};

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
};

// Request Headers
export const REQUEST_HEADERS = {
  CONTENT_TYPE: 'Content-Type',
  AUTHORIZATION: 'Authorization',
  ACCEPT: 'Accept',
  ACCEPT_LANGUAGE: 'Accept-Language',
};

// Content Types
export const CONTENT_TYPES = {
  JSON: 'application/json',
  FORM_DATA: 'multipart/form-data',
  URL_ENCODED: 'application/x-www-form-urlencoded',
};

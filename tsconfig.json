{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/screens/*": ["src/screens/*"], "@/services/*": ["src/services/*"], "@/stores/*": ["src/stores/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"], "@/hooks/*": ["src/hooks/*"], "@/constants/*": ["src/constants/*"]}}, "include": ["src/**/*", "index.js", "App.tsx", "__tests__/**/*", "e2e/**/*"], "exclude": ["node_modules", "android", "ios", "coverage", "dist"]}
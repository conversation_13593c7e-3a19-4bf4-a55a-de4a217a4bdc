name: CI/CD Pipeline

on:
  push:
    branches: [develop, main]
  pull_request:
    branches: [develop, main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run linting
        run: npm run lint
      
      - name: Run type checking
        run: npm run type-check
      
      - name: Run unit tests
        run: npm run test:coverage
      
      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
  
  build-android:
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'
      
      - name: Setup Android SDK
        uses: android-actions/setup-android@v3
      
      - name: Install dependencies
        run: npm ci
      
      - name: Cache Gradle packages
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-
      
      - name: Make gradlew executable
        run: chmod +x android/gradlew
      
      - name: Build Android APK
        run: |
          cd android
          ./gradlew assembleRelease
      
      - name: Upload APK artifact
        uses: actions/upload-artifact@v3
        with:
          name: app-release-${{ github.sha }}
          path: android/app/build/outputs/apk/release/app-release.apk
  
  build-ios:
    runs-on: macos-latest
    needs: test
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0'
          bundler-cache: true
      
      - name: Install dependencies
        run: npm ci
      
      - name: Install CocoaPods
        run: |
          cd ios
          pod install
      
      - name: Build iOS app
        run: |
          xcodebuild -workspace ios/DaltiProvider.xcworkspace \
            -scheme DaltiProvider \
            -configuration Release \
            -destination 'generic/platform=iOS' \
            -archivePath ios/build/DaltiProvider.xcarchive \
            archive
      
      - name: Export IPA
        run: |
          xcodebuild -exportArchive \
            -archivePath ios/build/DaltiProvider.xcarchive \
            -exportPath ios/build \
            -exportOptionsPlist ios/ExportOptions.plist
      
      - name: Upload IPA artifact
        uses: actions/upload-artifact@v3
        with:
          name: app-ios-${{ github.sha }}
          path: ios/build/DaltiProvider.ipa

  security-scan:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run security audit
        run: npm audit --audit-level moderate
      
      - name: Run dependency check
        uses: dependency-check/Dependency-Check_Action@main
        with:
          project: 'dalti-provider-react-native'
          path: '.'
          format: 'ALL'
      
      - name: Upload security scan results
        uses: actions/upload-artifact@v3
        with:
          name: security-scan-results
          path: reports/

  deploy-staging:
    runs-on: ubuntu-latest
    needs: [build-android, build-ios]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
      - name: Download Android APK
        uses: actions/download-artifact@v3
        with:
          name: app-release-${{ github.sha }}
          path: ./artifacts/
      
      - name: Deploy to Firebase App Distribution
        uses: wzieba/Firebase-Distribution-Github-Action@v1
        with:
          appId: ${{ secrets.FIREBASE_ANDROID_APP_ID }}
          serviceCredentialsFileContent: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
          groups: testers
          file: ./artifacts/app-release.apk
          releaseNotes: "Automated build from develop branch"

  deploy-production:
    runs-on: ubuntu-latest
    needs: [build-android, build-ios, security-scan]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v3
        with:
          path: ./artifacts/
      
      - name: Deploy to Google Play Console
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
          packageName: com.dalti.provider
          releaseFiles: ./artifacts/app-release-${{ github.sha }}/app-release.apk
          track: internal
          status: completed
      
      - name: Deploy to App Store Connect
        uses: apple-actions/upload-testflight-build@v1
        with:
          app-path: ./artifacts/app-ios-${{ github.sha }}/DaltiProvider.ipa
          issuer-id: ${{ secrets.APPSTORE_ISSUER_ID }}
          api-key-id: ${{ secrets.APPSTORE_API_KEY_ID }}
          api-private-key: ${{ secrets.APPSTORE_API_PRIVATE_KEY }}

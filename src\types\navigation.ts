import type { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import type { CompositeScreenProps } from '@react-navigation/native';
import type { StackScreenProps } from '@react-navigation/stack';

// Root Stack Navigator
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Onboarding: undefined;
};

// Auth Stack Navigator
export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  ResetPassword: { identifier: string; otp: string };
  OtpVerification: {
    identifier: string;
    type: 'registration' | 'password_reset';
  };
};

// Main Tab Navigator
export type MainTabParamList = {
  Dashboard: undefined;
  Appointments: undefined;
  Business: undefined;
  Customers: undefined;
  Messages: undefined;
};

// Dashboard Stack Navigator
export type DashboardStackParamList = {
  DashboardHome: undefined;
  Analytics: undefined;
  QuickActions: undefined;
};

// Appointments Stack Navigator
export type AppointmentsStackParamList = {
  AppointmentsList: undefined;
  AppointmentDetails: { appointmentId: number };
  CreateAppointment: undefined;
  EditAppointment: { appointmentId: number };
  Calendar: undefined;
};

// Business Stack Navigator
export type BusinessStackParamList = {
  BusinessHome: undefined;
  Locations: undefined;
  LocationDetails: { locationId: number };
  CreateLocation: undefined;
  EditLocation: { locationId: number };
  Services: undefined;
  ServiceDetails: { serviceId: number };
  CreateService: undefined;
  EditService: { serviceId: number };
  Queues: undefined;
  QueueDetails: { queueId: number };
  CreateQueue: undefined;
  EditQueue: { queueId: number };
  Profile: undefined;
  Settings: undefined;
};

// Customers Stack Navigator
export type CustomersStackParamList = {
  CustomersList: undefined;
  CustomerDetails: { customerId: number };
  CreateCustomer: undefined;
  EditCustomer: { customerId: number };
  CustomerHistory: { customerId: number };
};

// Messages Stack Navigator
export type MessagesStackParamList = {
  ConversationsList: undefined;
  Chat: { conversationId: number; customerName: string };
  NewConversation: undefined;
};

// Screen Props Types
export type RootStackScreenProps<T extends keyof RootStackParamList> =
  StackScreenProps<RootStackParamList, T>;

export type AuthStackScreenProps<T extends keyof AuthStackParamList> =
  StackScreenProps<AuthStackParamList, T>;

export type MainTabScreenProps<T extends keyof MainTabParamList> =
  CompositeScreenProps<
    BottomTabScreenProps<MainTabParamList, T>,
    RootStackScreenProps<keyof RootStackParamList>
  >;

export type DashboardStackScreenProps<T extends keyof DashboardStackParamList> =
  CompositeScreenProps<
    StackScreenProps<DashboardStackParamList, T>,
    MainTabScreenProps<keyof MainTabParamList>
  >;

export type AppointmentsStackScreenProps<
  T extends keyof AppointmentsStackParamList,
> = CompositeScreenProps<
  StackScreenProps<AppointmentsStackParamList, T>,
  MainTabScreenProps<keyof MainTabParamList>
>;

export type BusinessStackScreenProps<T extends keyof BusinessStackParamList> =
  CompositeScreenProps<
    StackScreenProps<BusinessStackParamList, T>,
    MainTabScreenProps<keyof MainTabParamList>
  >;

export type CustomersStackScreenProps<T extends keyof CustomersStackParamList> =
  CompositeScreenProps<
    StackScreenProps<CustomersStackParamList, T>,
    MainTabScreenProps<keyof MainTabParamList>
  >;

export type MessagesStackScreenProps<T extends keyof MessagesStackParamList> =
  CompositeScreenProps<
    StackScreenProps<MessagesStackParamList, T>,
    MainTabScreenProps<keyof MainTabParamList>
  >;

// Navigation Hook Types
declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}

# Development Environment Setup

## 🛠️ Prerequisites

### System Requirements
- **Node.js**: 18.x or higher (LTS recommended)
- **npm**: 9.x or higher (or yarn 1.22.x)
- **Git**: Latest version
- **VS Code**: Recommended IDE with extensions

### Platform-Specific Requirements

#### macOS (for iOS development)
- **Xcode**: 14.0 or higher
- **iOS Simulator**: Included with Xcode
- **CocoaPods**: `sudo gem install cocoapods`
- **Watchman**: `brew install watchman`

#### Windows/Linux (Android only)
- **Android Studio**: Latest version
- **Android SDK**: API Level 33 or higher
- **Java Development Kit**: JDK 11 or higher

#### Android Development (All platforms)
- **Android Studio**: Latest version
- **Android SDK**: API Level 33 or higher
- **Android Virtual Device (AVD)**: For testing

## 📱 React Native CLI Setup

### Install React Native CLI
```bash
# Install globally
npm install -g @react-native-community/cli

# Verify installation
npx react-native --version
```

### Verify Environment
```bash
# Check React Native environment
npx react-native doctor

# This will check:
# - Node.js version
# - npm/yarn version
# - Android SDK
# - Xcode (macOS only)
# - iOS Simulator (macOS only)
```

## 🔧 IDE Configuration

### VS Code Extensions
Install these essential extensions:

```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-json",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-react-native",
    "msjsdiag.vscode-react-native"
  ]
}
```

### VS Code Settings
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "emmet.includeLanguages": {
    "typescript": "typescriptreact",
    "javascript": "javascriptreact"
  }
}
```

## 🏗️ Project Initialization

### Create New React Native Project
```bash
# Create project with TypeScript template
npx react-native@latest init DaltiProviderRN --template react-native-template-typescript

# Navigate to project directory
cd DaltiProviderRN
```

### Install Core Dependencies
```bash
# Navigation
npm install @react-navigation/native @react-navigation/stack @react-navigation/bottom-tabs
npm install react-native-screens react-native-safe-area-context

# State Management
npm install zustand @tanstack/react-query

# HTTP Client
npm install axios

# Storage
npm install react-native-mmkv react-native-keychain

# Firebase
npm install @react-native-firebase/app @react-native-firebase/messaging @react-native-firebase/analytics

# UI Components
npm install react-native-vector-icons react-native-paper

# Utilities
npm install react-native-device-info react-native-permissions
npm install date-fns lodash

# Development Dependencies
npm install --save-dev @types/lodash @types/react-test-renderer
npm install --save-dev jest @testing-library/react-native
npm install --save-dev eslint prettier husky lint-staged
```

### TypeScript Configuration
```json
// tsconfig.json
{
  "extends": "@react-native/typescript-config/tsconfig.json",
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "baseUrl": "./src",
    "paths": {
      "@/*": ["*"],
      "@/components/*": ["components/*"],
      "@/screens/*": ["screens/*"],
      "@/services/*": ["services/*"],
      "@/utils/*": ["utils/*"],
      "@/types/*": ["types/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "android", "ios"]
}
```

## 🔥 Firebase Setup

### Firebase Project Configuration
1. **Create Firebase Project**: Use existing `dalti-prod` project
2. **Add Android App**: Package name `org.adscloud.dalti.provider`
3. **Add iOS App**: Bundle ID `org.adscloud.dalti.provider`
4. **Download Config Files**:
   - `google-services.json` for Android
   - `GoogleService-Info.plist` for iOS

### Android Firebase Setup
```bash
# Place google-services.json in android/app/
cp google-services.json android/app/

# Add to android/build.gradle
# classpath 'com.google.gms:google-services:4.3.15'

# Add to android/app/build.gradle
# apply plugin: 'com.google.gms.google-services'
```

### iOS Firebase Setup
```bash
# Place GoogleService-Info.plist in ios/ directory
cp GoogleService-Info.plist ios/

# Install iOS dependencies
cd ios && pod install && cd ..
```

## 📱 Platform Setup

### Android Setup
```bash
# Set environment variables (add to ~/.bashrc or ~/.zshrc)
export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
export PATH=$PATH:$ANDROID_HOME/platform-tools

# Create AVD (Android Virtual Device)
# Open Android Studio > AVD Manager > Create Virtual Device
# Recommended: Pixel 4 with API 33
```

### iOS Setup (macOS only)
```bash
# Install iOS dependencies
cd ios && pod install && cd ..

# Open iOS project in Xcode
open ios/DaltiProviderRN.xcworkspace
```

## 🧪 Testing Setup

### Jest Configuration
```json
// jest.config.js
module.exports = {
  preset: 'react-native',
  setupFilesAfterEnv: ['<rootDir>/src/test/setup.ts'],
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}',
  ],
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/test/**',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
```

### Test Setup File
```typescript
// src/test/setup.ts
import 'react-native-gesture-handler/jestSetup';

jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  Reanimated.default.call = () => {};
  return Reanimated;
});

jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

// Mock Firebase
jest.mock('@react-native-firebase/app', () => ({
  utils: () => ({
    FilePath: {
      PICTURES_DIRECTORY: '/tmp',
    },
  }),
}));
```

## 🔧 Development Scripts

### Package.json Scripts
```json
{
  "scripts": {
    "start": "react-native start",
    "android": "react-native run-android",
    "ios": "react-native run-ios",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint . --ext .js,.jsx,.ts,.tsx",
    "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix",
    "type-check": "tsc --noEmit",
    "clean": "react-native clean-project-auto",
    "build:android": "cd android && ./gradlew assembleRelease",
    "build:ios": "cd ios && xcodebuild -workspace DaltiProviderRN.xcworkspace -scheme DaltiProviderRN -configuration Release"
  }
}
```

## 🚀 Running the Application

### Development Mode
```bash
# Start Metro bundler
npm start

# Run on Android (in separate terminal)
npm run android

# Run on iOS (in separate terminal)
npm run ios

# Run on specific device
npm run android -- --deviceId=<device-id>
npm run ios -- --simulator="iPhone 14"
```

### Debug Mode
```bash
# Enable debug mode
# Android: Shake device or Cmd+M (emulator)
# iOS: Shake device or Cmd+D (simulator)

# Enable remote debugging
# Select "Debug" from developer menu
```

## 🔍 Debugging Tools

### Flipper Setup
```bash
# Install Flipper desktop app
# Download from: https://fbflipper.com/

# Install Flipper React Native plugin
npm install --save-dev react-native-flipper
```

### Reactotron Setup
```bash
# Install Reactotron
npm install --save-dev reactotron-react-native reactotron-redux

# Configure Reactotron
# Create src/config/ReactotronConfig.ts
```

## 📋 Environment Verification Checklist

### Pre-development Checklist
- [ ] Node.js 18+ installed
- [ ] React Native CLI installed
- [ ] Android Studio configured (for Android)
- [ ] Xcode configured (for iOS, macOS only)
- [ ] VS Code with extensions installed
- [ ] Firebase project configured
- [ ] Environment variables set
- [ ] AVD/Simulator created

### Test Environment
```bash
# Verify Android setup
npx react-native run-android

# Verify iOS setup (macOS only)
npx react-native run-ios

# Run tests
npm test

# Check linting
npm run lint

# Type checking
npm run type-check
```

## 🚨 Common Issues & Solutions

### Metro Bundler Issues
```bash
# Clear Metro cache
npx react-native start --reset-cache

# Clean project
npm run clean
```

### Android Build Issues
```bash
# Clean Android build
cd android && ./gradlew clean && cd ..

# Rebuild
npm run android
```

### iOS Build Issues
```bash
# Clean iOS build
cd ios && xcodebuild clean && cd ..

# Reinstall pods
cd ios && pod deintegrate && pod install && cd ..
```

## 🔄 Migration-Specific Setup

### Flutter to React Native Migration Tools
```bash
# Install migration helpers
npm install --save-dev @react-native-community/cli-doctor
npm install --save-dev react-native-bundle-visualizer

# Asset migration tools
npm install --save-dev react-native-asset
```

### Environment Variables
```bash
# Create .env file
echo "API_BASE_URL=https://dapi-test.adscloud.org:8443" > .env
echo "FIREBASE_PROJECT_ID=dalti-prod" >> .env
echo "APP_ENV=development" >> .env

# Install dotenv
npm install react-native-dotenv
```

### Code Generation Setup
```bash
# Install code generation tools
npm install --save-dev @graphql-codegen/cli
npm install --save-dev plop

# Setup API client generation
npm install openapi-typescript-codegen
```

## 📊 Performance Monitoring

### Setup Performance Tools
```bash
# Install performance monitoring
npm install @react-native-firebase/perf
npm install react-native-performance

# Bundle analysis
npm install --save-dev webpack-bundle-analyzer
```

### Memory Profiling
```bash
# Install memory profiling tools
npm install --save-dev why-did-you-render
npm install --save-dev @welldone-software/why-did-you-render
```

This comprehensive setup guide ensures a smooth development environment for the Dalti Provider React Native application with all necessary tools for migration, development, and performance monitoring.

{"name": "dalti-provider-react-native", "version": "1.0.0", "description": "Dalti Provider mobile application built with React Native - migrated from Flutter", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=__tests__", "test:integration": "jest --testPathPattern=integration", "test:e2e:ios": "detox test --configuration ios.sim.debug", "test:e2e:android": "detox test --configuration android.emu.debug", "test:e2e:build:ios": "detox build --configuration ios.sim.debug", "test:e2e:build:android": "detox build --configuration android.emu.debug", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write src/**/*.{ts,tsx}", "format:check": "prettier --check src/**/*.{ts,tsx}", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "xcodebuild -workspace ios/DaltiProvider.xcworkspace -scheme DaltiProvider -configuration Release"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@react-native-firebase/analytics": "^18.6.2", "@react-native-firebase/app": "^18.6.2", "@react-native-firebase/crashlytics": "^18.6.2", "@react-native-firebase/messaging": "^18.6.2", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@tanstack/react-query": "^5.8.4", "date-fns": "^2.30.0", "i18next": "^23.7.6", "react": "18.2.0", "react-hook-form": "^7.48.2", "react-i18next": "^13.5.0", "react-native": "0.73.0", "react-native-gesture-handler": "^2.14.0", "react-native-image-picker": "^7.0.3", "react-native-keychain": "^8.1.3", "react-native-localize": "^3.0.4", "react-native-maps": "^1.8.0", "react-native-permissions": "^4.0.5", "react-native-reanimated": "^3.6.0", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "^3.27.0", "react-native-svg": "^14.0.0", "react-native-vector-icons": "^10.0.2", "styled-components": "^6.1.1", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.73.0", "@react-native/metro-config": "^0.73.0", "@react-native/typescript-config": "^0.73.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.4.2", "@types/jest": "^29.5.8", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "babel-plugin-module-resolver": "^5.0.2", "detox": "^20.13.5", "eslint": "^8.19.0", "jest": "^29.2.1", "jest-environment-jsdom": "^29.7.0", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=18"}, "keywords": ["react-native", "mobile", "dalti", "provider", "business-management", "appointment-scheduling", "messaging"], "author": "Dalti Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Zedster07/dalti-provider-react-native.git"}, "bugs": {"url": "https://github.com/Zedster07/dalti-provider-react-native/issues"}, "homepage": "https://github.com/Zedster07/dalti-provider-react-native#readme"}
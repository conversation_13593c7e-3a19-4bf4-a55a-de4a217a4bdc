import { apiClient } from './apiClient';
import { API_ENDPOINTS } from '@/constants/api';
import type {
  LoginCredentials,
  LoginResponse,
  RegisterData,
  SendOtpRequest,
  VerifyOtpRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  ChangePasswordRequest,
  User,
} from '@/types/auth';

class AuthService {
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>(
      API_ENDPOINTS.AUTH.LOGIN,
      {
        identifier: credentials.identifier,
        password: credentials.password,
      }
    );
    return response.data;
  }

  async register(data: RegisterData): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>(
      API_ENDPOINTS.AUTH.REGISTER,
      data
    );
    return response.data;
  }

  async sendOtp(request: SendOtpRequest): Promise<void> {
    await apiClient.post(API_ENDPOINTS.AUTH.SEND_OTP, request);
  }

  async verifyOtp(request: VerifyOtpRequest): Promise<void> {
    await apiClient.post('/api/auth/provider/verify-otp', request);
  }

  async forgotPassword(request: ForgotPasswordRequest): Promise<void> {
    await apiClient.post(API_ENDPOINTS.AUTH.FORGOT_PASSWORD, request);
  }

  async resetPassword(request: ResetPasswordRequest): Promise<void> {
    await apiClient.post(API_ENDPOINTS.AUTH.RESET_PASSWORD, request);
  }

  async changePassword(request: ChangePasswordRequest): Promise<void> {
    await apiClient.post(API_ENDPOINTS.PROVIDER.CHANGE_PASSWORD, request);
  }

  async updateProfile(data: Partial<User>): Promise<User> {
    const response = await apiClient.put<User>(
      API_ENDPOINTS.PROVIDER.UPDATE_PROFILE,
      data
    );
    return response.data;
  }

  async refreshToken(): Promise<{ sessionId: string }> {
    const response = await apiClient.post<{ sessionId: string }>(
      API_ENDPOINTS.AUTH.REFRESH_TOKEN
    );
    return response.data;
  }

  async logout(): Promise<void> {
    await apiClient.post(API_ENDPOINTS.AUTH.LOGOUT);
  }

  async getProfile(): Promise<User> {
    const response = await apiClient.get<User>(API_ENDPOINTS.PROVIDER.PROFILE);
    return response.data;
  }

  async uploadAvatar(imageUri: string): Promise<{ avatarUrl: string }> {
    const formData = new FormData();
    formData.append('avatar', {
      uri: imageUri,
      type: 'image/jpeg',
      name: 'avatar.jpg',
    } as any);

    const response = await apiClient.post<{ avatarUrl: string }>(
      API_ENDPOINTS.PROVIDER.UPLOAD_AVATAR,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  }
}

export const authService = new AuthService();

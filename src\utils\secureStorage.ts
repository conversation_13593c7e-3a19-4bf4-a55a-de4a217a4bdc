import * as Keychain from 'react-native-keychain';

class SecureStorage {
  private serviceName = 'DaltiProvider';

  async setItem(key: string, value: string): Promise<void> {
    try {
      await Keychain.setInternetCredentials(
        `${this.serviceName}_${key}`,
        key,
        value
      );
    } catch (error) {
      console.error(`Failed to store ${key} securely:`, error);
      throw new Error(`Failed to store ${key} securely`);
    }
  }

  async getItem(key: string): Promise<string | null> {
    try {
      const credentials = await Keychain.getInternetCredentials(
        `${this.serviceName}_${key}`
      );
      
      if (credentials && credentials.password) {
        return credentials.password;
      }
      
      return null;
    } catch (error) {
      console.error(`Failed to retrieve ${key} from secure storage:`, error);
      return null;
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      await Keychain.resetInternetCredentials(`${this.serviceName}_${key}`);
    } catch (error) {
      console.error(`Failed to remove ${key} from secure storage:`, error);
      throw new Error(`Failed to remove ${key} from secure storage`);
    }
  }

  async clear(): Promise<void> {
    try {
      // Get all stored keys and remove them
      const commonKeys = ['sessionId', 'user', 'provider', 'language', 'theme'];
      
      await Promise.all(
        commonKeys.map(key => this.removeItem(key).catch(() => {}))
      );
    } catch (error) {
      console.error('Failed to clear secure storage:', error);
      throw new Error('Failed to clear secure storage');
    }
  }

  async hasItem(key: string): Promise<boolean> {
    try {
      const value = await this.getItem(key);
      return value !== null;
    } catch (error) {
      return false;
    }
  }

  // Utility methods for common operations
  async setObject(key: string, object: any): Promise<void> {
    const jsonString = JSON.stringify(object);
    await this.setItem(key, jsonString);
  }

  async getObject<T>(key: string): Promise<T | null> {
    try {
      const jsonString = await this.getItem(key);
      if (jsonString) {
        return JSON.parse(jsonString) as T;
      }
      return null;
    } catch (error) {
      console.error(`Failed to parse object for key ${key}:`, error);
      return null;
    }
  }

  // Biometric authentication methods
  async setBiometricItem(key: string, value: string): Promise<void> {
    try {
      await Keychain.setInternetCredentials(
        `${this.serviceName}_${key}`,
        key,
        value,
        {
          accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_CURRENT_SET,
          authenticationType: Keychain.AUTHENTICATION_TYPE.DEVICE_PASSCODE_OR_BIOMETRICS,
        }
      );
    } catch (error) {
      console.error(`Failed to store ${key} with biometric protection:`, error);
      throw new Error(`Failed to store ${key} with biometric protection`);
    }
  }

  async getBiometricItem(key: string): Promise<string | null> {
    try {
      const credentials = await Keychain.getInternetCredentials(
        `${this.serviceName}_${key}`,
        {
          authenticationType: Keychain.AUTHENTICATION_TYPE.DEVICE_PASSCODE_OR_BIOMETRICS,
          showModal: true,
          kLocalizedFallbackTitle: 'Use Passcode',
        }
      );
      
      if (credentials && credentials.password) {
        return credentials.password;
      }
      
      return null;
    } catch (error) {
      console.error(`Failed to retrieve ${key} with biometric authentication:`, error);
      return null;
    }
  }

  // Check if biometric authentication is available
  async isBiometricAvailable(): Promise<boolean> {
    try {
      const biometryType = await Keychain.getSupportedBiometryType();
      return biometryType !== null;
    } catch (error) {
      return false;
    }
  }

  // Get supported biometry type
  async getBiometryType(): Promise<Keychain.BIOMETRY_TYPE | null> {
    try {
      return await Keychain.getSupportedBiometryType();
    } catch (error) {
      return null;
    }
  }
}

export const secureStorage = new SecureStorage();
